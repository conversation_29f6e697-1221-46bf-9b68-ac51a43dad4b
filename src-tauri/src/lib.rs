// 1. 使用 #[path] 属性来显式指定模块的路径, 并将其声明为公共
#[path = "../api/mod.rs"]
pub mod api;

use std::fs;
use std::sync::{Arc, Mutex};
use tauri::{Manager, State};

// 定义一个状态结构体来保存端口号
pub struct PortState(pub Arc<Mutex<Option<u16>>>);

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn get_backend_port(state: State<'_, PortState>) -> Result<u16, String> {
    // 尝试获取端口号，最多等待10秒
    for _ in 0..100 {
        if let Some(port) = *state.0.lock().unwrap() {
            return Ok(port);
        }
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    }
    Err("Failed to get backend port in time".to_string())
}

// 文件信息结构体
#[derive(serde::Serialize, serde::Deserialize)]
pub struct FileInfo {
    pub name: String,
    pub size: u64,
    pub path: String,
    pub content: Vec<u8>,
    pub mime_type: String,
}

#[tauri::command]
async fn select_and_read_file(app_handle: tauri::AppHandle) -> Result<FileInfo, String> {
    use std::sync::mpsc;
    use std::sync::{Arc, Mutex};
    use tauri_plugin_dialog::DialogExt;

    // 创建一个通道来接收文件路径
    let (tx, rx) = mpsc::channel();
    let tx = Arc::new(Mutex::new(Some(tx)));

    // 使用文件对话框选择文件
    app_handle
        .dialog()
        .file()
        .add_filter("All Files", &["*"])
        .add_filter("Images", &["png", "jpg", "jpeg", "gif", "bmp", "svg"])
        .add_filter("Documents", &["pdf", "doc", "docx", "txt", "md"])
        .add_filter("Data", &["json", "xml", "csv", "xlsx"])
        .pick_file(move |result| {
            if let Some(tx) = tx.lock().unwrap().take() {
                let _ = tx.send(result);
            }
        });

    // 等待文件选择结果
    let file_path = rx
        .recv()
        .map_err(|_| "Failed to receive file selection result")?
        .ok_or("No file selected")?;

    // 读取文件信息和内容
    let path_buf = file_path
        .into_path()
        .map_err(|e| format!("Invalid file path: {}", e))?;
    let path_str = path_buf.to_string_lossy().to_string();
    let file_name = path_buf
        .file_name()
        .ok_or("Invalid file name")?
        .to_string_lossy()
        .to_string();

    let metadata =
        fs::metadata(&path_buf).map_err(|e| format!("Failed to read file metadata: {}", e))?;

    let content = fs::read(&path_buf).map_err(|e| format!("Failed to read file content: {}", e))?;

    // 根据文件扩展名推断 MIME 类型
    let mime_type = match path_buf.extension().and_then(|ext| ext.to_str()) {
        Some("png") => "image/png",
        Some("jpg") | Some("jpeg") => "image/jpeg",
        Some("gif") => "image/gif",
        Some("bmp") => "image/bmp",
        Some("svg") => "image/svg+xml",
        Some("pdf") => "application/pdf",
        Some("json") => "application/json",
        Some("xml") => "application/xml",
        Some("csv") => "text/csv",
        Some("txt") => "text/plain",
        Some("md") => "text/markdown",
        _ => "application/octet-stream",
    }
    .to_string();

    Ok(FileInfo {
        name: file_name,
        size: metadata.len(),
        path: path_str,
        content,
        mime_type,
    })
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let port_state = PortState(Arc::new(Mutex::new(None)));

    tauri::Builder::default()
        .manage(port_state)
        .setup(|app| {
            let app_handle = app.handle().clone();
            let port_state = app_handle.state::<PortState>();
            let port_clone = Arc::clone(&port_state.0);

            tauri::async_runtime::spawn(async move {
                let current_dir = std::env::current_dir().expect("Failed to get current directory");
                let path = current_dir;
                let port = api::start_server(false, path).await;

                // 将端口号存储在状态中
                let mut port_guard = port_clone.lock().unwrap();
                *port_guard = Some(port);
            });
            Ok(())
        })
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_dialog::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            get_backend_port,
            select_and_read_file
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
