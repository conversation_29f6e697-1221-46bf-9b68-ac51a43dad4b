use crate::api::AppState;
use axum::{
    extract::{Multipart, State},
    http::StatusCode,
    response::<PERSON><PERSON>,
};
use chrono::Utc;
use serde_json::{json, Value};
use std::sync::Arc;
use uuid::Uuid;

#[derive(serde::Serialize, serde::Deserialize)]
pub struct UploadedFileInfo {
    pub id: String,
    pub name: String,
    pub size: u64,
    pub mime_type: String,
    pub upload_time: String,
    pub content: Vec<u8>,
}

// 处理文件上传
pub async fn upload_file(
    State(_state): State<Arc<AppState>>,
    mut multipart: Multipart,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    let mut uploaded_files = Vec::new();

    while let Some(field) = multipart.next_field().await.map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(json!({
                "code": 400,
                "message": format!("Failed to read multipart field: {}", e),
                "data": null
            })),
        )
    })? {
        let _name = field.name().unwrap_or("unknown").to_string();
        let file_name = field.file_name().unwrap_or("unknown").to_string();
        let content_type = field
            .content_type()
            .unwrap_or("application/octet-stream")
            .to_string();

        let data = field.bytes().await.map_err(|e| {
            (
                StatusCode::BAD_REQUEST,
                Json(json!({
                    "code": 400,
                    "message": format!("Failed to read file data: {}", e),
                    "data": null
                })),
            )
        })?;

        let file_info = UploadedFileInfo {
            id: Uuid::new_v4().to_string(),
            name: file_name,
            size: data.len() as u64,
            mime_type: content_type,
            upload_time: Utc::now().to_rfc3339(),
            content: data.to_vec(),
        };

        // 这里可以选择将文件保存到数据库或文件系统
        // 为了演示，我们暂时只返回文件信息
        uploaded_files.push(json!({
            "id": file_info.id,
            "name": file_info.name,
            "size": file_info.size,
            "mime_type": file_info.mime_type,
            "upload_time": file_info.upload_time,
            // 注意：在实际应用中，通常不会直接返回文件内容，而是返回文件的访问URL
            "has_content": true
        }));
    }

    if uploaded_files.is_empty() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({
                "code": 400,
                "message": "No files uploaded",
                "data": null
            })),
        ));
    }

    Ok(Json(json!({
        "code": 200,
        "message": "Files uploaded successfully",
        "data": {
            "files": uploaded_files,
            "count": uploaded_files.len()
        }
    })))
}

// 获取文件信息（不包含内容）
pub async fn get_file_info(
    State(_state): State<Arc<AppState>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 这里可以从数据库获取文件列表
    // 为了演示，返回一个示例响应
    Ok(Json(json!({
        "code": 200,
        "message": "File info retrieved successfully",
        "data": {
            "files": [],
            "total": 0
        }
    })))
}

// 下载文件
pub async fn download_file(
    State(_state): State<Arc<AppState>>,
) -> Result<Json<Value>, (StatusCode, Json<Value>)> {
    // 这里可以根据文件ID从数据库或文件系统获取文件
    // 为了演示，返回一个示例响应
    Ok(Json(json!({
        "code": 200,
        "message": "File download endpoint",
        "data": null
    })))
}
