use crate::api::handler::component::{
    add_component, add_component_type, delete_component, delete_component_type, get_component,
    get_component_type_list, list_component, update_component, update_component_type,
};
use crate::api::handler::diagram::{get_diagram, save_diagram};
use crate::api::handler::file::{download_file, get_file_info, upload_file};
use crate::api::AppState;
use axum::routing::{get, post};
use axum::Router;
use std::sync::Arc;

pub fn api_v1_component() -> Router<Arc<AppState>> {
    Router::new()
        .route("/type/list", get(get_component_type_list))
        .route("/type/add", post(add_component_type))
        .route("/type/update", post(update_component_type))
        .route("/type/delete", post(delete_component_type))
        .route("/add", post(add_component))
        .route("/list", get(list_component))
        .route("/get", get(get_component))
        .route("/update", post(update_component))
        .route("/delete", post(delete_component))
        .route("/diagram/save", post(save_diagram))
        .route("/diagram/get", get(get_diagram))
}

pub fn api_v1_file() -> Router<Arc<AppState>> {
    Router::new()
        .route("/upload", post(upload_file))
        .route("/info", get(get_file_info))
        .route("/download/:id", get(download_file))
}
