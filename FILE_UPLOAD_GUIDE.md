# 文件上传功能使用指南

本项目新增了完整的文件上传功能，支持本地文件选择、读取和上传到服务器。

## 🚀 功能特性

- **本地文件选择**: 使用 Tauri 文件对话框选择本地文件
- **文件信息读取**: 获取文件名、大小、MIME类型和内容
- **HTTP 文件上传**: 支持单文件和多文件上传到服务器
- **组合操作**: 一键选择本地文件并上传到服务器
- **类型安全**: 完整的 TypeScript 类型定义

## 📁 新增文件

### 后端文件
- `src-tauri/api/handler/file.rs` - 文件上传处理器
- `src-tauri/src/lib.rs` - 新增 `select_and_read_file` Tauri 命令

### 前端文件
- `src/api/types.ts` - 新增文件相关类型定义
- `src/api/index.ts` - 新增文件上传 API 函数
- `src/components/FileUpload.vue` - 文件上传组件
- `src/views/FileTest/index.vue` - 文件上传测试页面

## 🔧 配置更新

### Cargo.toml 更新
```toml
# 新增依赖
tauri-plugin-dialog = "2"
axum = { version = "0.8.4", features = ["macros", "multipart"] }
```

### 路由更新
- 新增 `/api/v1/file/*` 路由
- 新增 `/file-test` 前端路由

## 📖 API 使用方法

### 1. 选择并读取本地文件

```typescript
import { selectAndReadFile } from '@/api'

try {
  const fileInfo = await selectAndReadFile()
  console.log('文件信息:', fileInfo)
  // 返回: { name, size, path, content, mime_type }
} catch (error) {
  console.error('文件选择失败:', error)
}
```

### 2. 上传文件到服务器

```typescript
import { uploadFile, uploadFiles } from '@/api'

// 上传单个文件
const file = new File([blob], 'filename.txt', { type: 'text/plain' })
const result = await uploadFile(file)

// 上传多个文件
const files = [file1, file2, file3]
const result = await uploadFiles(files)
```

### 3. 一键选择并上传

```typescript
import { selectAndUploadFile } from '@/api'

try {
  const result = await selectAndUploadFile()
  console.log('上传结果:', result.data.files)
} catch (error) {
  console.error('操作失败:', error)
}
```

## 🌐 HTTP API 端点

| 方法 | 端点 | 描述 |
|------|------|------|
| POST | `/api/v1/file/upload` | 上传文件 |
| GET | `/api/v1/file/info` | 获取文件列表 |
| GET | `/api/v1/file/download/:id` | 下载文件 |

## 🎯 Tauri 命令

| 命令 | 描述 | 返回类型 |
|------|------|----------|
| `select_and_read_file` | 选择并读取本地文件 | `FileInfo` |

## 📝 类型定义

```typescript
interface FileInfo {
  name: string;        // 文件名
  size: number;        // 文件大小（字节）
  path: string;        // 文件路径
  content: number[];   // 文件内容（字节数组）
  mime_type: string;   // MIME类型
}

interface UploadedFileInfo {
  id: string;          // 文件ID
  name: string;        // 文件名
  size: number;        // 文件大小
  mime_type: string;   // MIME类型
  upload_time: string; // 上传时间
  has_content: boolean; // 是否包含内容
}
```

## 🧪 测试页面

访问 `/file-test` 路由可以看到完整的文件上传功能演示，包括：

1. **选择本地文件并上传** - 一键操作
2. **拖拽上传** - 支持多文件拖拽
3. **仅选择文件** - 不上传，只获取文件信息

## 🔍 支持的文件类型

- **图片**: PNG, JPG, JPEG, GIF, BMP, SVG
- **文档**: PDF, DOC, DOCX, TXT, MD
- **数据**: JSON, XML, CSV, XLSX
- **其他**: 所有文件类型

## ⚠️ 注意事项

1. **文件大小限制**: 建议单文件不超过 100MB
2. **安全性**: 生产环境中应添加文件类型验证和病毒扫描
3. **存储**: 当前示例将文件内容保存在内存中，生产环境建议保存到文件系统或对象存储
4. **权限**: 确保应用有读取本地文件的权限

## 🚀 快速开始

1. 启动开发服务器：
   ```bash
   npm run dev:web
   ```

2. 访问测试页面：
   ```
   http://localhost:1420/file-test
   ```

3. 尝试不同的文件上传方式

## 🔧 自定义扩展

可以根据需要扩展功能：

- 添加文件预览功能
- 实现文件管理（删除、重命名等）
- 添加进度条显示
- 支持断点续传
- 集成云存储服务

---

这个文件上传功能为项目提供了完整的本地文件访问和服务器上传能力，可以满足大多数文件处理需求。
