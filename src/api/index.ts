import request from './request';
import type { ApiResponse, Component, FileInfo, UploadedFileInfo } from './types';
import { invoke } from '@tauri-apps/api/core';

export function login(data: any) {
  return request({
    url: '/user/login',
    method: 'post',
    data,
  });
}

export function getComponentTypeList() {
  return request({
    url: '/api/v1/component/type/list',
    method: 'get',
  });
}

export function getComponentList({ component_type_id = 1 }: {
  component_type_id?: number
} = {}): Promise<ApiResponse<Component[]>> {
  return request({
    url: '/api/v1/component/list',
    method: 'get',
    params: {
      component_type_id
    }
  });
}

export function addComponent(data: {
  name: string;
  keyword: string;
  component_type_id: number;
  description?: string;
}) {
  return request({
    url: '/api/v1/component/add',
    method: 'post',
    data,
  });
}

export function updateComponent(data: {
  component_id: string;
  name: string;
  keyword: string;
  component_type_id: number;
  description?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/update',
    method: 'post',
    data,
  });
}

export function deleteComponent(data: {
  component_id: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/delete',
    method: 'post',
    data,
  });
}

export function getComponent(params: {
  component_id: string;
}): Promise<ApiResponse<Component>> {
  return request({
    url: '/api/v1/component/get',
    method: 'get',
    params,
  });
}

// 保存图数据
export function saveDiagram(data: {
  component_id: string;
  property: object;
  definition: object;
  diagram: object;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/diagram/save',
    method: 'post',
    data,
  });
}

// 获取图数据
export function getDiagram(params: {
  component_id: string;
}): Promise<ApiResponse<{
  component_id: string;
  create_time: string;
  definition: object;
  [key: string]: any;
}>> {
  return request({
    url: '/api/v1/component/diagram/get',
    method: 'get',
    params,
  });
}

// 新建元件类型
export function addComponentType(data: {
  name: string;
  remark?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/add',
    method: 'post',
    data,
  });
}

// 更新元件类型
export function updateComponentType(data: {
  id: number;
  name: string;
  remark?: string;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/update',
    method: 'post',
    data,
  });
}

// 删除元件类型
export function deleteComponentType(data: {
  id: number;
}): Promise<ApiResponse> {
  return request({
    url: '/api/v1/component/type/delete',
    method: 'post',
    data,
  });
}

// ==================== 文件相关 API ====================

// 使用 Tauri 命令选择并读取本地文件
export async function selectAndReadFile(): Promise<FileInfo> {
  try {
    const fileInfo = await invoke<FileInfo>('select_and_read_file');
    return fileInfo;
  } catch (error) {
    throw new Error(`文件选择失败: ${error}`);
  }
}

// 上传文件到服务器
export function uploadFile(file: File): Promise<ApiResponse<{
  files: UploadedFileInfo[];
  count: number;
}>> {
  const formData = new FormData();
  formData.append('file', file);

  return request({
    url: '/api/v1/file/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 上传多个文件到服务器
export function uploadFiles(files: File[]): Promise<ApiResponse<{
  files: UploadedFileInfo[];
  count: number;
}>> {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`file_${index}`, file);
  });

  return request({
    url: '/api/v1/file/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 从本地文件创建 File 对象并上传
export async function selectAndUploadFile(): Promise<ApiResponse<{
  files: UploadedFileInfo[];
  count: number;
}>> {
  try {
    // 首先使用 Tauri 命令选择文件
    const fileInfo = await selectAndReadFile();

    // 将文件内容转换为 Blob
    const uint8Array = new Uint8Array(fileInfo.content);
    const blob = new Blob([uint8Array], { type: fileInfo.mime_type });

    // 创建 File 对象
    const file = new File([blob], fileInfo.name, { type: fileInfo.mime_type });

    // 上传文件
    return await uploadFile(file);
  } catch (error) {
    throw new Error(`文件选择和上传失败: ${error}`);
  }
}

// 获取文件信息列表
export function getFileList(): Promise<ApiResponse<{
  files: UploadedFileInfo[];
  total: number;
}>> {
  return request({
    url: '/api/v1/file/info',
    method: 'get',
  });
}

// 下载文件
export function downloadFile(fileId: string): Promise<ApiResponse> {
  return request({
    url: `/api/v1/file/download/${fileId}`,
    method: 'get',
  });
}