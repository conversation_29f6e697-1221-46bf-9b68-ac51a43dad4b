export interface ApiResponse<T = any> {
  code: number;
  data: T;
  message: string;
}

export interface Component {
  id: string;
  name: string;
  keyword: string;
  description: string;
  create_time: string;
  update_time: string;
  is_diagram: number;//1: 表示存在对应的图数据，2:表示不存在对应的图数据
  component_type_id: number;
}

export interface FileInfo {
  name: string;
  size: number;
  path: string;
  content: number[]; // Vec<u8> 在 TypeScript 中表示为 number[]
  mime_type: string;
}

export interface UploadedFileInfo {
  id: string;
  name: string;
  size: number;
  mime_type: string;
  upload_time: string;
  has_content: boolean;
}