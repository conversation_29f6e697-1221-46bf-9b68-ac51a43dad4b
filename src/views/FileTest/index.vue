<template>
  <div class="file-test-page">
    <div class="page-header">
      <h1>文件上传功能测试</h1>
      <p>这个页面演示了如何使用本项目的文件上传功能</p>
    </div>
    
    <FileUpload />
    
    <el-card class="api-info-card">
      <template #header>
        <div class="card-header">
          <span>API 使用说明</span>
        </div>
      </template>
      
      <div class="api-section">
        <h3>1. Tauri 命令 API</h3>
        <el-code-block language="typescript">
// 选择并读取本地文件（返回文件信息和内容）
import { selectAndReadFile } from '@/api'

const fileInfo = await selectAndReadFile()
console.log('文件信息:', fileInfo)
// 返回: { name, size, path, content, mime_type }
        </el-code-block>
      </div>
      
      <div class="api-section">
        <h3>2. HTTP 文件上传 API</h3>
        <el-code-block language="typescript">
// 上传单个文件
import { uploadFile } from '@/api'

const file = new File([blob], 'filename.txt', { type: 'text/plain' })
const result = await uploadFile(file)

// 上传多个文件
import { uploadFiles } from '@/api'

const files = [file1, file2, file3]
const result = await uploadFiles(files)
        </el-code-block>
      </div>
      
      <div class="api-section">
        <h3>3. 组合使用：选择本地文件并上传</h3>
        <el-code-block language="typescript">
// 一键选择本地文件并上传到服务器
import { selectAndUploadFile } from '@/api'

const result = await selectAndUploadFile()
console.log('上传结果:', result.data.files)
        </el-code-block>
      </div>
      
      <div class="api-section">
        <h3>4. 后端 API 端点</h3>
        <ul>
          <li><code>POST /api/v1/file/upload</code> - 文件上传</li>
          <li><code>GET /api/v1/file/info</code> - 获取文件列表</li>
          <li><code>GET /api/v1/file/download/:id</code> - 下载文件</li>
        </ul>
      </div>
      
      <div class="api-section">
        <h3>5. Tauri 命令</h3>
        <ul>
          <li><code>select_and_read_file</code> - 选择并读取本地文件</li>
        </ul>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import FileUpload from '@/components/FileUpload.vue'
</script>

<style scoped>
.file-test-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  color: #409eff;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.api-info-card {
  margin-top: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-section {
  margin: 20px 0;
}

.api-section h3 {
  color: #409eff;
  margin-bottom: 15px;
}

.api-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.api-section li {
  margin: 5px 0;
}

.api-section code {
  background-color: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.el-code-block {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
}
</style>
