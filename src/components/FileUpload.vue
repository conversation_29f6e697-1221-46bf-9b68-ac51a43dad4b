<template>
  <div class="file-upload-container">
    <el-card class="upload-card">
      <template #header>
        <div class="card-header">
          <span>文件上传示例</span>
        </div>
      </template>

      <div class="upload-section">
        <h3>方式一：选择本地文件并上传</h3>
        <el-button 
          type="primary" 
          @click="handleSelectAndUpload"
          :loading="uploading"
          :disabled="uploading"
        >
          <el-icon><Upload /></el-icon>
          选择文件并上传
        </el-button>
      </div>

      <el-divider />

      <div class="upload-section">
        <h3>方式二：拖拽或点击上传</h3>
        <el-upload
          class="upload-dragger"
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          :file-list="fileList"
          multiple
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持多种文件格式：图片、文档、数据文件等
            </div>
          </template>
        </el-upload>
        
        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button 
            type="success" 
            @click="handleUploadFiles"
            :loading="uploading"
            :disabled="uploading"
          >
            上传选中文件
          </el-button>
          <el-button @click="clearFiles">清空</el-button>
        </div>
      </div>

      <el-divider />

      <div class="upload-section">
        <h3>方式三：仅选择本地文件（不上传）</h3>
        <el-button 
          type="info" 
          @click="handleSelectOnly"
          :loading="selecting"
          :disabled="selecting"
        >
          <el-icon><Folder /></el-icon>
          仅选择文件
        </el-button>
      </div>

      <!-- 文件信息显示 -->
      <div v-if="selectedFileInfo" class="file-info">
        <h4>选中的文件信息：</h4>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="文件名">{{ selectedFileInfo.name }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(selectedFileInfo.size) }}</el-descriptions-item>
          <el-descriptions-item label="MIME类型">{{ selectedFileInfo.mime_type }}</el-descriptions-item>
          <el-descriptions-item label="文件路径">{{ selectedFileInfo.path }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 上传结果显示 -->
      <div v-if="uploadResults.length > 0" class="upload-results">
        <h4>上传结果：</h4>
        <el-table :data="uploadResults" style="width: 100%">
          <el-table-column prop="name" label="文件名" />
          <el-table-column prop="size" label="大小" :formatter="formatSizeColumn" />
          <el-table-column prop="mime_type" label="类型" />
          <el-table-column prop="upload_time" label="上传时间" />
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, UploadFilled, Folder } from '@element-plus/icons-vue'
import type { UploadFile } from 'element-plus'
import { selectAndReadFile, selectAndUploadFile, uploadFiles } from '@/api'
import type { FileInfo, UploadedFileInfo } from '@/api/types'

// 响应式数据
const uploading = ref(false)
const selecting = ref(false)
const fileList = ref<UploadFile[]>([])
const selectedFileInfo = ref<FileInfo | null>(null)
const uploadResults = ref<UploadedFileInfo[]>([])

// 选择本地文件并上传
const handleSelectAndUpload = async () => {
  uploading.value = true
  try {
    const result = await selectAndUploadFile()
    if (result.code === 200) {
      ElMessage.success('文件上传成功！')
      uploadResults.value = result.data.files
    } else {
      ElMessage.error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(`上传失败: ${error}`)
  } finally {
    uploading.value = false
  }
}

// 仅选择本地文件
const handleSelectOnly = async () => {
  selecting.value = true
  try {
    const fileInfo = await selectAndReadFile()
    selectedFileInfo.value = fileInfo
    ElMessage.success('文件选择成功！')
  } catch (error) {
    console.error('文件选择失败:', error)
    ElMessage.error(`文件选择失败: ${error}`)
  } finally {
    selecting.value = false
  }
}

// 处理文件变化（拖拽上传）
const handleFileChange = (file: UploadFile) => {
  // Element Plus 会自动管理文件列表
}

// 上传选中的文件
const handleUploadFiles = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }

  uploading.value = true
  try {
    const files = fileList.value.map((item: UploadFile) => item.raw!).filter(Boolean)
    const result = await uploadFiles(files)
    
    if (result.code === 200) {
      ElMessage.success(`成功上传 ${result.data.count} 个文件！`)
      uploadResults.value = result.data.files
      clearFiles()
    } else {
      ElMessage.error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(`上传失败: ${error}`)
  } finally {
    uploading.value = false
  }
}

// 清空文件列表
const clearFiles = () => {
  fileList.value = []
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 表格格式化器
const formatSizeColumn = (row: UploadedFileInfo) => {
  return formatFileSize(row.size)
}
</script>

<style scoped>
.file-upload-container {
  padding: 20px;
}

.upload-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-section {
  margin: 20px 0;
}

.upload-section h3 {
  margin-bottom: 15px;
  color: #409eff;
}

.upload-dragger {
  width: 100%;
}

.upload-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
}

.file-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.upload-results {
  margin-top: 20px;
}

.upload-results h4 {
  margin-bottom: 15px;
  color: #67c23a;
}
</style>
