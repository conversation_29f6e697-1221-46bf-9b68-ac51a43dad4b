import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import Layout from '@/layout/index.vue';
import EditorLayout from '@/layout/EditorLayout.vue';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login/index.vue'),
  },
  {
    path: '/',
    component: Layout,
    redirect: '/home',
    children: [
      {
        path: 'home',
        name: 'Home',
        component: () => import('@/views/Home/index.vue'),
      },
      {
        path: 'simulation',
        name: 'Simulation',
        component: () => import('@/views/Simulation/index.vue'),
      },
      {
        path: 'material',
        name: 'Material',
        component: () => import('@/views/Material/index.vue'),
      },
      {
        path: 'test',
        name: 'Customization',
        component: () => import('@/views/Customization/index.vue'),
      },
      {
        path: 'file-test',
        name: 'FileTest',
        component: () => import('@/views/FileTest/index.vue'),
      },
    ],
  },
  {
    path: '/component-editor/:id?',
    name: 'ComponentEditor',
    component: EditorLayout,
    children: [
      {
        path: '',
        component: () => import('@/views/ComponentEditor/index.vue'),
      },
    ],
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;